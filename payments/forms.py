from django import forms

from payments.models import BankAccount


class TransferForm(forms.Form):
    from_account = forms.ModelChoiceField(
        queryset=BankAccount.objects.all(),
        label="From account",
        required=True
    )
    to_account = forms.ModelChoiceField(
        queryset=BankAccount.objects.all(),
        label="To account",
        required=True
    )
    amount = forms.IntegerField(
        min_value=1,
        label="Amount",
        required=True
    )

    def clean_to_account(self):
        to_account = self.cleaned_data["to_account"]
        from_account = self.cleaned_data["from_account"]

        if to_account == from_account:
            raise forms.ValidationError("Cannot transfer to the same account")

        return to_account
