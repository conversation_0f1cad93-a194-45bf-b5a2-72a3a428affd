from django.db import transaction

from payments.exceptions import InvalidAmount, SameAccount, InsufficientFunds
from payments.models import BankAccount


class TransferService:
    def transfer(
            self,
            from_account: BankAccount,
            to_account: BankAccount,
            amount: int
    ) -> None:
        if amount <= 0:
            raise InvalidAmount("Amount must be greater than 0")

        if from_account == to_account:
            raise SameAccount("Cannot transfer to the same account")

        with transaction.atomic():
            locked_accounts = (
                BankAccount.objects
                .select_for_update()
                .in_bulk([from_account.id, to_account.id])
            )
            account_from = locked_accounts[from_account.id]
            account_to = locked_accounts[to_account.id]

            if account_from.balance < amount:
                raise InsufficientFunds("Insufficient funds")

            account_from.balance -= amount
            account_to.balance += amount
            account_from.save(update_fields=["balance"])
            account_to.save(update_fields=["balance"])
