from django.contrib import messages
from django.db import transaction
from django.db.models import F
from django.http import HttpRequest, HttpResponse
from django.shortcuts import render, redirect
from django.views import View, generic

from payments.exceptions import InsufficientFunds, SameAccount, InvalidAmount, TransferError
from payments.forms import TransferForm
from payments.models import BankAccount
from payments.services import TransferService


class TransferView(View):
    transfer_service = TransferService()

    def get(self, request: HttpRequest) -> HttpResponse:
        return render(
            request=request,
            template_name="payments/transfer.html",
            context={"form": TransferForm()}
        )

    def post(self, request: HttpRequest) -> HttpResponse:
        form = TransferForm(request.POST)

        if form.is_valid():
            from_account = form.cleaned_data["from_account"]
            to_account = form.cleaned_data["to_account"]
            amount = form.cleaned_data["amount"]

            try:
                self.transfer_service.transfer(from_account, to_account, amount)
            except InvalidAmount:
                form.add_error("amount", "Invalid amount")
            except InsufficientFunds:
                form.add_error("amount", "Insufficient funds")
            except SameAccount:
                form.add_error("to_account", "Cannot transfer to the same account")
            except TransferError:
                form.add_error(None, "An error occurred")
            else:
                messages.success(request, "Transfer successful")

                return redirect("payments:bank-account-list")

            return redirect("payments:bank-account-list")

        return render(
            request=request,
            template_name="payments/transfer.html",
            context={"form": form}
        )

class BankAccountListView(generic.ListView):
    template_name = "payments/bank_account_list.html"
    queryset = BankAccount.objects.select_related("user")
